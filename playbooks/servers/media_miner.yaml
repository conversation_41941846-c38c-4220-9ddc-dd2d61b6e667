---
# Media Miner Playbook
# 
# This playbook sets up a complete media server with the following components:
# - Music management: Lidarr
# - TV show management: Sonarr
# - Movie management: Radarr
# - Ebook management: Readarr
# - Subtitle management: Bazarr
# - Media encoding: Tdarr/Unmanic/FileFlows
# - Indexer management: Prowlarr
# - Download client: NZBGet

- name: Media Miner - Complete Media Server Setup
  hosts: "{{ hosts | default('moria') }}"
  gather_facts: true
  become: true
  
  vars_files:
    - vars/media_miner.yml
  
  pre_tasks:
    - name: Update package cache
      ansible.builtin.package:
        update_cache: yes
      changed_when: false
      when: ansible_os_family == "Debian"
      
    - name: Ensure required directories exist
      ansible.builtin.file:
        path: "{{ item }}"
        state: directory
        mode: '0755'
      loop:
        - "/mnt/local-data"
        - "/mnt/local-docker-data"
        - "/mnt/nasa-p1z2-data"
        - "/mnt/nasa-p1z2-docker-data"

    - name: Mount Media NFS volume
      ansible.posix.mount:
        src: "{{ media_nfs_server }}:{{ media_nfs_path }}"
        path: "{{ media_mount_path }}"
        opts: "rw,soft"
        state: mounted
        fstype: nfs

    - name: Mount docker-data Config NFS volume
      ansible.posix.mount:
        src: "{{ config_nfs_server }}:{{ config_nfs_path }}"
        path: "{{ config_mount_path }}"
        opts: "rw,soft"
        state: mounted
        fstype: nfs
        
    - name: Ensure host directory exists in the docker-data NFS mount
      ansible.builtin.file:
        path: "{{ config_host_dir }}"
        state: directory
        mode: '0755'
  
  roles:
    - role: podman-host
      tags: 
        - container
        - setup
    
    - role: media-lidarr
      tags:
        - media
        - music
    
    - role: media-sonarr
      tags:
        - media
        - tv

    - role: media-radarr
      tags:
        - media
        - movies
    
#    - role: media-readarr
#      tags:
#        - media
#        - books
#      when: enable_readarr | default(true)
#
#    - role: media-bazarr
#      tags:
#        - media
#        - subtitles
#      when: enable_bazarr | default(true)
#
    - role: media-prowlarr
      tags:
        - media
        - indexer
#
    - role: media-nzbget
      tags:
        - media
        - download
#
#    - role: media-encoding
#      tags:
#        - media
#        - encoding
#      when: enable_encoding | default(true)
#      vars:
#        encoding_app: "{{ encoding_application | default('tdarr') }}"

  post_tasks:
    - name: Verify services are running
      ansible.builtin.command: podman ps
      register: container_status
      changed_when: false
      
    - name: Display container status
      ansible.builtin.debug:
        var: container_status.stdout_lines
        
    - name: Check for container health
      ansible.builtin.uri:
        url: "http://localhost:{{ item.port }}/api/v1/system/status"
        method: GET
        status_code: 200
        timeout: 5
      register: health_check
      failed_when: false
      changed_when: false
      loop:
        - { name: "Sonarr", port: 8989 }
        - { name: "Radarr", port: 7878 }
        - { name: "Lidarr", port: 8686 }
      
    - name: Display service health status
      ansible.builtin.debug:
        msg: "{{ item.item.name }} is {{ 'UP' if item.status == 200 else 'DOWN or STARTING' }}"
      loop: "{{ health_check.results }}"
