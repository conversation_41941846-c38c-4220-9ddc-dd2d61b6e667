---
# Variables for media_miner playbook

# Feature flags - enable/disable components
enable_readarr: true
enable_bazarr: true
enable_encoding: true

# Encoding application selection (tdarr, unmanic, or fileflows)
encoding_application: "tdarr"

# NFS configuration
media_nfs_server: "*************"
media_nfs_path: "/mnt/nasa-p1z2/Media"
media_mount_path: "/mnt/nasa-p1z2-data"
config_nfs_server: "*************"
config_nfs_path: "/mnt/nasa-p1z2/docker-data"
config_mount_path: "/mnt/nasa-p1z2-docker-data"
config_host_dir: "{{ config_mount_path }}/host-{{ ansible_hostname }}"

# Local paths
local_data_path: "/mnt/local-data"
local_docker_data_path: "/mnt/local-docker-data"

# Container configuration
container_user_id: "1000"
container_group_id: "1000"

# Service ports
sonarr_port: 8989
radarr_port: 7878
lidarr_port: 8686
readarr_port: 8787
bazarr_port: 6767
prowlarr_port: 9696
nzbget_port: 6789
tdarr_port: 8265
unmanic_port: 8888
fileflows_port: 5000