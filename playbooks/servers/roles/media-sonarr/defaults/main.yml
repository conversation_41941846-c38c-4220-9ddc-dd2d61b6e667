---
# defaults file for media-sonarr

# NFS configuration
media_nfs_server: "*************"
media_nfs_path: "/mnt/nasa-p1z2/Media"
media_mount_path: "/mnt/nasa-p1z2-media"
config_nfs_server: "*************"
config_nfs_path: "/mnt/nasa-p1z2/docker-data"
config_mount_path: "/mnt/nasa-p1z2-docker-data"
sonarr_config_dir: "{{ config_mount_path }}/host-{{ ansible_hostname }}/sonarr/config"

# Local paths
local_data_path: "/mnt/local-data"
local_docker_data_path: "/mnt/local-docker-data"

# Container configuration
container_user_id: "1000"
container_group_id: "1000"
sonarr_image: "lscr.io/linuxserver/sonarr:latest"
sonarr_port: 8989

# Container volumes
sonarr_volumes:
  - "{{ sonarr_config_dir }}:/config"
  - "{{ media_mount_path }}:/data"

# Container environment variables
sonarr_environment:
  PUID: "{{ container_user_id }}"
  PGID: "{{ container_group_id }}"
  TZ: "America/Denver"
