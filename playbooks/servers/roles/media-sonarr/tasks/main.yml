---
# tasks file for media-sonarr

- name: Mount Media NFS volume
  ansible.posix.mount:
    src: "{{ media_nfs_server }}:{{ media_nfs_path }}"
    path: "{{ media_mount_path }}"
    opts: "rw,soft"
    state: mounted
    fstype: nfs

- name: Mount docker-data Config NFS volume
  ansible.posix.mount:
    src: "{{ config_nfs_server }}:{{ config_nfs_path }}"
    path: "{{ config_mount_path }}"
    opts: "rw,soft"
    state: mounted
    fstype: nfs

- name: Ensure host app config directory exists
  ansible.builtin.file:
    path: "{{ sonarr_config_dir }}"
    state: directory
    mode: '0755'

- name: Create local directories
  ansible.builtin.file:
    path: "{{ item.path }}"
    state: directory
    mode: "{{ item.mode | default('0755') }}"
    owner: "{{ container_user_id }}"
    group: "{{ container_group_id }}"
  loop:
    - path: "{{ local_data_path }}/usenet"
      comment: "Local SSD storage for fast operations"
    - path: "{{ local_docker_data_path }}/sonarr/config"

- name: Configure firewall rules for Sonarr
  ansible.builtin.iptables:
    chain: INPUT
    protocol: "{{ item.protocol }}"
    destination_port: "{{ item.port }}"
    jump: ACCEPT
    comment: "ALLOW {{ item.comment }}"
  loop:
    - port: "{{ sonarr_port }}"
      protocol: tcp
      comment: "expose port for sonarr ui http"

- name: Pull the latest Sonarr image
  containers.podman.podman_image:
    name: "{{ sonarr_image }}"

- name: Stop and remove existing sonarr container
  containers.podman.podman_container:
    name: sonarr
    state: absent

# TV Series Search App
- name: Start sonarr container
  containers.podman.podman_container:
    name: sonarr
    image: "{{ sonarr_image }}"
    state: present
    recreate: true
    force_restart: yes
    restart_policy: on-failure:5
    network: bridge
    ports:
      - "{{ sonarr_port }}:{{ sonarr_port }}"
    volumes: "{{ sonarr_volumes }}"
    env: "{{ sonarr_environment }}"
