---
# tasks file for media-radarr

- name: Mount Media NFS volume
  ansible.posix.mount:
    src: "{{ media_nfs_server }}:{{ media_nfs_path }}"
    path: "{{ media_mount_path }}"
    opts: "rw,soft"
    state: mounted
    fstype: nfs

- name: Mount docker-data Config NFS volume
  ansible.posix.mount:
    src: "{{ config_nfs_server }}:{{ config_nfs_path }}"
    path: "{{ config_mount_path }}"
    opts: "rw,soft"
    state: mounted
    fstype: nfs

- name: Ensure host app config directory exists
  ansible.builtin.file:
    path: "{{ radarr_config_dir }}"
    state: directory
    mode: '0755'

- name: Create local directories
  ansible.builtin.file:
    path: "{{ item.path }}"
    state: directory
    mode: "{{ item.mode | default('0755') }}"
    owner: "{{ container_user_id }}"
    group: "{{ container_group_id }}"
  loop:
    - path: "{{ local_data_path }}/usenet"
      comment: "Local SSD storage for fast operations"
    - path: "{{ local_docker_data_path }}/radarr/config"

- name: Configure firewall rules for Radarr
  ansible.builtin.iptables:
    chain: INPUT
    protocol: "{{ item.protocol }}"
    destination_port: "{{ item.port }}"
    jump: ACCEPT
    comment: "ALLOW {{ item.comment }}"
  loop:
    - port: "{{ radarr_port }}"
      protocol: tcp
      comment: "expose port for radarr ui http"

- name: Pull the latest Radarr image
  containers.podman.podman_image:
    name: "{{ radarr_image }}"

- name: Stop and remove existing radarr container
  containers.podman.podman_container:
    name: radarr
    state: absent

# Movie Search App
- name: Start radarr container
  containers.podman.podman_container:
    name: radarr
    image: "{{ radarr_image }}"
    state: present
    recreate: true
    force_restart: yes
    restart_policy: on-failure:5
    network: bridge
    ports:
      - "{{ radarr_port }}:{{ radarr_port }}"
    volumes: "{{ radarr_volumes }}"
    env: "{{ radarr_environment }}"
