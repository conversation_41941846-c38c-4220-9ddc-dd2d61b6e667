---
# defaults file for media-prowlarr

# NFS configuration
media_nfs_server: "*************"
media_nfs_path: "/mnt/nasa-p1z2/Media"
media_mount_path: "/mnt/nasa-p1z2-media"
config_nfs_server: "*************"
config_nfs_path: "/mnt/nasa-p1z2/docker-data"
config_mount_path: "/mnt/nasa-p1z2-docker-data"

# Local paths
local_data_path: "/mnt/local-data"
local_docker_data_path: "/mnt/local-docker-data"

# Container configuration
container_user_id: "1000"
container_group_id: "1000"
prowlarr_image: "lscr.io/linuxserver/prowlarr:latest"
prowlarr_port: 9696

# Container volumes
prowlarr_volumes:
  - "{{ config_mount_path }}/{{ ansible_hostname }}/prowlarr/config:/config"

# Container environment variables
prowlarr_environment:
  PUID: "{{ container_user_id }}"
  PGID: "{{ container_group_id }}"
  TZ: "America/Denver"