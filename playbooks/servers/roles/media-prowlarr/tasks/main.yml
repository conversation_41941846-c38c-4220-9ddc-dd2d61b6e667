---
# tasks file for media-prowlarr
# complete management of your indexers with no per app Indexer setup required

- name: Mount Media NFS volume
  ansible.posix.mount:
    src: "{{ media_nfs_server }}:{{ media_nfs_path }}"
    path: "{{ media_mount_path }}"
    opts: "rw,soft"
    state: mounted
    fstype: nfs

- name: Mount docker-data Config NFS volume
  ansible.posix.mount:
    src: "{{ config_nfs_server }}:{{ config_nfs_path }}"
    path: "{{ config_mount_path }}"
    opts: "rw,soft"
    state: mounted
    fstype: nfs

- name: Create local directories
  ansible.builtin.file:
    path: "{{ item.path }}"
    state: directory
    mode: "{{ item.mode | default('0755') }}"
    owner: "{{ container_user_id }}"
    group: "{{ container_group_id }}"
  loop:
    - path: "{{ local_docker_data_path }}/prowlarr/config"

- name: Configure firewall rules for Prowlarr
  ansible.builtin.iptables:
    chain: INPUT
    protocol: "{{ item.protocol }}"
    destination_port: "{{ item.port }}"
    jump: ACCEPT
    comment: "ALLOW {{ item.comment }}"
  loop:
    - port: "{{ prowlarr_port }}"
      protocol: tcp
      comment: "expose port for prowlarr ui http"

- name: Pull the latest Prowlarr image
  containers.podman.podman_image:
    name: "{{ prowlarr_image }}"

- name: Stop and remove existing prowlarr container
  containers.podman.podman_container:
    name: prowlarr
    state: absent

# Indexer management app
- name: Start prowlarr container
  containers.podman.podman_container:
    name: prowlarr
    image: "{{ prowlarr_image }}"
    state: present
    recreate: true
    force_restart: yes
    restart_policy: on-failure:5
    network: bridge
    ports:
      - "{{ prowlarr_port }}:{{ prowlarr_port }}"
    volumes: "{{ prowlarr_volumes }}"
    env: "{{ prowlarr_environment }}"
