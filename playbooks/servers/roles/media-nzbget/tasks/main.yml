#SPDX-License-Identifier: MIT-0
---
# tasks file for media-nzbget

- name: Mount Media NFS volume
  ansible.posix.mount:
    src: "{{ media_nfs_server }}:{{ media_nfs_path }}"
    path: "{{ media_mount_path }}"
    opts: "rw,soft"
    state: mounted
    fstype: nfs

- name: Mount docker-data Config NFS volume
  ansible.posix.mount:
    src: "{{ config_nfs_server }}:{{ config_nfs_path }}"
    path: "{{ config_mount_path }}"
    opts: "rw,soft"
    state: mounted
    fstype: nfs

- name: Create local directories
  ansible.builtin.file:
    path: "{{ item.path }}"
    state: directory
    mode: "{{ item.mode | default('0755') }}"
    owner: "{{ container_user_id }}"
    group: "{{ container_group_id }}"
  loop:
    - path: "{{ local_data_path }}/usenet"
      comment: "Local SSD storage for fast operations"
    - path: "{{ local_docker_data_path }}/nzbget/config"

- name: Configure firewall rules for NZBGet
  ansible.builtin.iptables:
    chain: INPUT
    protocol: "{{ item.protocol }}"
    destination_port: "{{ item.port }}"
    jump: ACCEPT
    comment: "ALLOW {{ item.comment }}"
  loop:
    - port: "{{ nzbget_port }}"
      protocol: tcp
      comment: "expose port for nzbget ui http"

- name: Pull the latest NZBGet image
  containers.podman.podman_image:
    name: "{{ nzbget_image }}"

- name: Stop and remove existing nzbget container
  containers.podman.podman_container:
    name: nzbget
    state: absent

# NZB Download App
- name: Start nzbget container
  containers.podman.podman_container:
    name: nzbget
    image: "{{ nzbget_image }}"
    state: present
    recreate: true
    force_restart: yes
    restart_policy: on-failure:5
    network: bridge
    ports:
      - "{{ nzbget_port }}:{{ nzbget_port }}"
    volumes: "{{ nzbget_volumes }}"
    env: "{{ nzbget_environment }}"
