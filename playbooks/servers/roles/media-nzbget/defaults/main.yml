---
# defaults file for media-nzbget

# NFS configuration
media_nfs_server: "*************"
media_nfs_path: "/mnt/nasa-p1z2/Media"
media_mount_path: "/mnt/nasa-p1z2-media"
config_nfs_server: "*************"
config_nfs_path: "/mnt/nasa-p1z2/docker-data"
config_mount_path: "/mnt/nasa-p1z2-docker-data"

# Local paths
local_data_path: "/mnt/local-data"
local_docker_data_path: "/mnt/local-docker-data"

# Container configuration
container_user_id: "1000"
container_group_id: "1000"
nzbget_image: "lscr.io/linuxserver/nzbget:latest"
nzbget_port: 6789

# Container volumes
nzbget_volumes:
  - "{{ config_mount_path }}/{{ ansible_hostname }}/nzbget/config:/config"
  - "{{ media_mount_path }}:/data"

# Container environment variables
nzbget_environment:
  PUID: "{{ container_user_id }}"
  PGID: "{{ container_group_id }}"
  TZ: "America/Denver"
  NZBGET_USER: "NZBER"
  NZBGET_PASS: "NZBERGET"
